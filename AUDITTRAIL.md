# Audit Trail

## Wednesday, July 02, 2025

### Added Modbus Register Definitions from Documentation

**Objective**: Add selected Modbus register definitions (0x0047-0x004E) from documentation.txt to the existing YAML configuration file.

**Changes Made**:

1. **Added Status and Configuration Registers**: Added 7 new register definitions to the sensors section of `homeassistant-configurations.yaml`:
   - `Inverter_Enabled` (0x0047) - uint16, read-only, 0=Disabled 1=Enabled
   - `Charger_Enabled` (0x0048) - uint16, read-only, 0=Disabled 1=Enabled
   - `Sell_Enabled` (0x0049) - uint16, read-only, 0=Disabled 1=Enabled
   - `Active_Faults_Flag` (0x004B) - uint16, read-only, 0=No Faults 1=Active Faults
   - `Active_Warnings_Flag` (0x004C) - uint16, read-only, 0=No Warnings 1=Active Warnings
   - `Charge_Mode_Status` (0x004D) - uint16, read-only, 0=Stand alone 1=Primary 2=Secondary
   - `Configuration_Errors` (0x004E) - uint32, read-only

**Technical Implementation**:

- **Register Addresses**: Used hexadecimal format (0x0047-0x004E) as specified in documentation
- **Data Types**: Configured uint16 for most registers, uint32 for Configuration_Errors as per documentation
- **Access Permissions**: All registers configured as read-only (input_type: holding) matching 'r' designation in documentation
- **Scale and Offset**: Applied scale factor 1.0 and offset 0.0 as specified in documentation
- **Device Class**: Used 'enum' device class for status/flag registers to indicate discrete values
- **Comments**: Added inline comments with value descriptions from documentation
- **Slave ID**: Configured all registers for slave ID 10 (primary inverter)

**Configuration Details**:

- **input_type: holding**: Specifies reading from holding registers
- **Comments included**: Added value descriptions as comments for each register (e.g., "0=Disabled 1=Enabled")
- **Proper YAML formatting**: Maintained consistent indentation and structure with existing configuration
- **Logical grouping**: Added section comment to identify the new register group

**Benefits**:

- Provides monitoring capability for critical inverter status flags
- Enables fault and warning detection through Home Assistant
- Allows tracking of charge mode status and configuration errors
- Follows existing YAML configuration patterns and naming conventions
- Includes comprehensive documentation through inline comments

**Outcome**: Successfully added 7 new Modbus register definitions to the Home Assistant configuration, enabling monitoring of inverter status, charger status, sell status, fault flags, warning flags, charge mode, and configuration errors.

## Tuesday, June 24, 2025

### Grid Support Sensors - Added Write Capabilities

**Objective**: Transform the two Grid Support sensors from read-only to read-write capable while maintaining their current functionality.

**Changes Made**:

1. **Preserved existing read-only sensors**: Kept the original `Inv1_Grid_Support` and `Inv2_Grid_Support` sensors intact to maintain backward compatibility and existing read functionality.

2. **Added new switch entities for write capabilities**:
   - `Inv1_Grid_Support_Control` (slave: 10, address: 435)
   - `Inv2_Grid_Support_Control` (slave: 12, address: 435)

**Technical Implementation**:

- **Entity Type**: Added new `switches` section to the Modbus configuration
- **Write Type**: Used `holding` register type for write operations
- **Commands**: Configured `command_on: 1` and `command_off: 0` for binary control
- **Verification**: Added `verify` configuration to read back the written values for confirmation
- **Scan Interval**: Maintained 20-second scan interval consistent with original sensors
- **Address**: Used same address (435) as the original sensors for both read and write operations

**Configuration Details**:

- **write_type: holding**: Enables writing to holding registers using the `write_register` function
- **verify section**: Ensures written values are confirmed by reading back from the same register
- **state_on/state_off**: Defines expected values (1/0) when reading back the register state

**Benefits**:

- Maintains existing read-only functionality for backward compatibility
- Adds new write-capable entities for control operations
- Follows Home Assistant Modbus integration best practices
- Includes verification to ensure write operations are successful
- Uses proper YAML formatting and indentation

**Outcome**: Successfully added write capabilities while preserving existing read functionality. The configuration now supports both monitoring (via sensors) and control (via switches) of the Grid Support functionality on both inverters.

### Grid Support Deployment Guide Created

**Objective**: Provide comprehensive deployment instructions for implementing the new Grid Support switch controls.

**File Created**: `Grid_Support_Deployment_Guide.md`

**Content Includes**:

1. **Pre-deployment steps**: YAML validation, backup procedures, current state documentation
2. **Deployment process**: Step-by-step restart and configuration application
3. **Entity verification**: Confirming new switch entities are properly created
4. **UI configuration**: Dashboard setup with both visual editor and YAML methods
5. **Testing procedures**: Safe testing protocols for Grid Support toggle functionality
6. **Troubleshooting**: Common issues and diagnostic procedures
7. **Automation integration**: Example automations for coordinated control
8. **Safety considerations**: Important warnings about Grid Support control impacts
9. **Maintenance procedures**: Ongoing monitoring and rollback procedures

**Key Safety Features**:

- Emphasis on testing during low-load conditions
- Backup and rollback procedures
- Monitoring guidelines for system stability
- Debug logging configuration for troubleshooting

**Outcome**: Created comprehensive deployment guide suitable for users with basic Home Assistant knowledge, including all necessary safety considerations and step-by-step procedures for successful implementation.
