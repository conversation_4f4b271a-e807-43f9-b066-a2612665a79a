```
Table 3 Configuration and Status Registers
| Modbus Address | Name | Type | read/write (r/w) | Units | Scale | Offset | Notes |
|---|---|---|---|---|---|---|---|
| 0x01AA | Charge Block Start | uint16 | rw | min | 1.0 | 0.0 | |
| 0x01AB | Charge Block Stop | uint16 | rw | min | 1.0 | 0.0 | |
| 0x01AC | Load Shave Stop | uint16 | rw | min | 1.0 | 0.0 | |
| 0x01AD | Load Shave Start | uint16 | rw | min | 1.0 | 0.0 | |
| 0x01AE | AC1 Low Frequency | uint16 | rw | Hz | 0.01 | 0.0 | |
| 0x01AF | AC2 Low Frequency | uint16 | rw | Hz | 0.01 | 0.0 | |
| 0x01B0 | AC1 High Frequency | uint16 | rw | Hz | 0.01 | 0.0 | |
| 0x01B1 | AC2 High Frequency | uint16 | rw | Hz | 0.01 | 0.0 | |
| 0x01B2 | Load Shave | uint16 | rw | | 1.0 | 0.0 | 0=Disable 1=Enable |
| 0x01B3 | Grid Support | uint16 | rw | | 1.0 | 0.0 | 0=Disable 1=Enable |
...
| 0x0047 |Inverter Enabled|uint16|r| |1.0|0.0|0=Disabled 1=Enabled
| 0x0048 |Charger Enabled|uint16|r| |1.0|0.0|0=Disabled 1=Enabled
| 0x0049 |Sell Enabled|uint16|r| |1.0|0.0|0=Disabled 1=Enabled
| 0x004B |Active Faults Flag|uint16|r| |1.0|0.0|0=No Faults 1=Active Faults
| 0x004C |Active Warnings Flag|uint16|r| |1.0|0.0|0=No Warnings 1=Active Warnings
| 0x004D |Charge Mode Status|uint16|r| |1.0|0.0|0=Stand alone 1=Primary 2=Secondary
| 0x004E |Configuration Errors|uint32|r| |1.0|0.0|
...
| 0x007A | Inverter Status | uint16 | r | | 1.0 | 0.0 | See section 10
| 0x007B | Charger Status | uint16 | r | | 1.0 | 0.0 | See section 11

```

Section 10: Inverter Status
Inverter Status can report one of the following values:
• 1024=Invert
• 1025=AC Pass Through
• 1026=APS Only
• 1027=Load Sense
• 1028=Inverter Disabled
• 1029=Load Sense Ready
• 1030=Engaging Inverter
• 1031=Invert Fault
• 1032=Inverter Standby
• 1033=Grid-Tied
• 1034=Grid Support
• 1035=Gen Support
• 1036=Sell-to-Grid
• 1037=Load Shaving
• 1038=Grid Frequency Stabilization
• 1039=AC Coupling
• 1040=Reverse Ibatt